<?php
// Auto-generated category index
// Category: ptsd-myth

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'PTSD Mythology';
$meta_description = 'Challenging conventional wisdom about PTSD and mental health through research, advocacy, and lived experience. A collection of writings on mental health reform.';
$meta_keywords = 'ptsd-myth, posts, A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$related_posts = [];

// Category posts data
$category_posts = array (
  0 => 
  array (
    'title' => 'Is Cannabis Helping or Harming You? A Guide for Traumatic Stress',
    'author' => 'A. A. Chips',
    'date' => '2025-05-20',
    'excerpt' => 'Challenging conventional wisdom about PTSD and mental health through research, advocacy, and lived experience. A collection of writings on mental health reform.',
    'url' => 'cannabis-helping-or-harming.php',
    'tags' => 
    array (
      0 => 'mental health',
      1 => 'ptsd',
      2 => 'psychology',
      3 => 'advocacy',
      4 => 'research',
    ),
    'filename' => 'cannabis-helping-or-harming',
    'thumbnail' => 'https://www.banyantreatmentcenter.com/wp-content/uploads/2022/09/ptsd-history.png',
  ),
  1 => 
  array (
    'title' => 'Traumatic Brain Injury in Homeless People is Underrecognized',
    'author' => 'Susan Fitzgerald - NeurologyToday',
    'date' => '2020-02-06',
    'excerpt' => 'Traumatic brain injury (TBI) is a common and often underrecognized problem in homeless people, according to a new study.',
    'url' => 'tbi-homeless.php',
    'tags' => 
    array (
      0 => 'homelessness',
      1 => 'advocacy',
    ),
    'filename' => 'tbi-homeless',
    'thumbnail' => 'https://phoenix-mhs.com/wp-content/uploads/2020/12/Traumatic-brain-injury.jpg',
  ),
);

// Generate content
ob_start();
?>
<div class="category-index">
    <header class="category-header">
        <h1><?php echo htmlspecialchars($page_title); ?></h1>
    </header>

    <div class="category-content">
        <?php echo <<<'HTML'
<h2>PTSD Mythology
</h2>

<p>Here is an archive mainly of repurposed homework writings I have done on mental health reform. The title is provocative - and intentionally so.</p>

<p>This collection challenges conventional wisdom about PTSD, trauma, and mental health treatment. Through research, advocacy, and lived experience, these writings explore:</p>

<ul><li>Alternative perspectives on trauma and healing
</li>
<p><li>Critiques of current mental health systems
</li></p>
<p><li>Research on underrecognized conditions
</li></p>
<p><li>Advocacy for better treatment approaches
</li></p>
<p><li>The intersection of mental health and social justice
</li></ul></p>

<p>The goal isn't to dismiss the reality of trauma or the need for mental health support, but to question assumptions, explore alternatives, and advocate for more effective, compassionate approaches to healing.</p>
HTML;
        ?>
    </div>

    <div class="post-grid">
        <?php foreach ($category_posts as $post): ?>
            <div class="post-card">
                <a href="<?php echo htmlspecialchars($post['url']); ?>" class="post-card-link">
                <div class="post-card-thumbnail">
                    <?php if (isset($post['thumbnail']) && $post['thumbnail']): ?>
                        <?php if (preg_match('/^https?:\/\//', $post['thumbnail'])): ?>
                            <img src="<?php echo htmlspecialchars($post['thumbnail']); ?>" 
                                 alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img">
                        <?php else: ?>
                            <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo htmlspecialchars($post['thumbnail']); ?>" 
                                 alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img">
                        <?php endif; ?>
                    <?php else: ?>
                        <?php $placeholder_images = ['placeholder1.jpg', 'placeholder2.jpg', 'placeholder3.jpg', 'placeholder4.jpg']; ?>
                        <?php $random_placeholder = $placeholder_images[array_rand($placeholder_images)]; ?>
                        <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo $random_placeholder; ?>" 
                             alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img placeholder">
                    <?php endif; ?>
                </div>
                <div class="post-card-content">
                    <h3 class="post-card-title"><?php echo htmlspecialchars($post['title']); ?></h3>
                    <p class="post-card-excerpt"><?php echo htmlspecialchars(is_string($post['excerpt']) ? $post['excerpt'] : ''); ?></p>
                    <div class="post-card-meta">
                        <?php if (isset($post['author']) && $post['author'] && is_string($post['author'])): ?>
                            <span class="post-author">By <?php echo htmlspecialchars($post['author']); ?></span>
                        <?php endif; ?>
                        <?php if (isset($post['date']) && $post['date']): ?>
                            <span class="post-date"><?php echo is_string($post['date']) ? htmlspecialchars($post['date']) : ''; ?></span>
                        <?php endif; ?>
                    </div>
                    <?php if (!empty($post['tags'])): ?>
                        <div class="post-card-tags">
                            <?php foreach ($post['tags'] as $tag): ?>
                                <span class="tag"><?php echo htmlspecialchars($tag); ?></span>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                </a>
            </div>
        <?php endforeach; ?>
    </div>
</div>
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>