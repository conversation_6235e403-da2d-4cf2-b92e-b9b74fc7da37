<?php
// Auto-generated blog post
// Source: learn-attack.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Learn Attack: Could Humans One Day Die on Mars';
$meta_description = 'Prepare to have your knowledge receptors jammed with facts on ‘Learn Attack,’ a brand-new science series from ClickHole. In this episode, <PERSON> asks: Could humans colonize Mars? The answer might surprise you.';
$meta_keywords = 'humor, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = $paths['base_path'] . 'img/thumbs/' . '../../img/clickhole.jpg';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Learn Attack: Could Humans One Day Die on Mars',
  'date' => '2017-03-06',
  'excerpt' => 'Prepare to have your knowledge receptors jammed with facts on ‘Learn Attack,’ a brand-new science series from ClickHole. In this episode, David asks: Could humans colonize Mars? The answer might surprise you.',
  'tags' => 
  array (
    0 => 'humor',
  ),
  'categories' => 
  array (
    0 => 'Humor',
  ),
  'thumbnail' => '../../img/clickhole.jpg',
  'source_file' => 'content\\humor\\learn-attack.md',
);

// Raw content
$post_content = '<iframe width="560" height="315" src="https://www.youtube.com/embed/DnqdZDmDLlQ?si=zVi1ntVHr2pxGiFM" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

<h1>Learn Attack: Could Humans One Day Die On Mars?</h1>

<p><a href="https://www.youtube.com/@ClickHole" class="external-link">ClickHole</a></p>
<p>299K subscribers</p>
<p>151,157 views Mar 6, 2017</p>

<p>Prepare to have your knowledge receptors jammed with facts on ‘Learn Attack,’ a brand-new science series from ClickHole. In this episode, David asks: Could humans colonize Mars? The answer might surprise you. Subscribe to ClickHole <a href="https://www.youtube.com/redirect?event=video_description&redir_token=QUFFLUhqa1ZROGJVc3Z0dTEzUHBkVXJRSlljRlI3SU11QXxBQ3Jtc0tsVFZsZm0yRzVVQTZTR1kzMWxwZDhsTVo5UVFyODJJWFFJMXo0cTJkZmtaaG95YmZPQVc1VHNOVnBmZWxPWEpFU1IyYlU4WVpOaDlBUWVUVWJOYnVwS1RrRUJlNlRYTUtXSkF6NjdsM3lubnFoeFpYMA&q=http%3A%2F%2Fbit.ly%2F1qugIEe&v=DnqdZDmDLlQ" class="external-link">http://bit.ly/1qugIEe</a> Like ClickHole on Facebook <a href="https://www.youtube.com/redirect?event=video_description&redir_token=QUFFLUhqbGU5UnlVY1J5c1BwczVVYllBMk9sY19MOUdnUXxBQ3Jtc0ttbWN0bWFZZ0xwdkx5cC11NFYxR080OEM4V1pEM2ZsM1lqODAwaXJpWnBlVDJhWFQtRHF1TU50bkNfd3lZU0NVMzRPWFhHenNpMTMzdElub0VvWUlQbGk0Nk45RjNCUU8zVzFaZENjVFpuZW5jbnBIYw&q=https%3A%2F%2Fwww.fb.com%2Fclickhole&v=DnqdZDmDLlQ" class="external-link">https://www.fb.com/clickhole</a> Follow ClickHole on Twitter   <a href="https://www.youtube.com/redirect?event=video_description&redir_token=QUFFLUhqbUF4Qm82ckI2cFpnUWlOMnY3Uy1lVFRZRi1iZ3xBQ3Jtc0ttaGotc04zWjhRd0RCV2VwNFNiZE5jVmFCSXFmRERNRTN2ZUFCb0ZjdkgyWHBLbUtybnRqeDhxM0JKQTlfME1UMGZNVkg3djdiRmFUbWtzNnZ6Zzl1NkxLREEwR3Z3YVJnUXNoM3BOdHhnTGtaRUJkRQ&q=https%3A%2F%2Ftwitter.com%2FClickHole&v=DnqdZDmDLlQ" class="external-link"><img src="https://www.gstatic.com/youtube/img/watch/social_media/twitter_1x_v2.png" alt=""> / clickhole</a></p>

<p><!-- Image missing: mars.png --></p>
<p><em>[Image: mars.png - not found]</em></p>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>